import { motion } from "framer-motion";
import { Card, CardContent } from "../../ui/card";

export const HowItWorks = () => {
  const timelineSteps = [
    {
      title: "Tell us your preferences",
      description: "Share your travel dates, budget, and interests.",
      isFirst: true,
      isLast: false,
    },
    {
      title: "Get a personalized itinerary",
      description: "Receive a detailed plan with flight and hotel options.",
      isFirst: false,
      isLast: false,
    },
    {
      title: "Book your trip",
      description: "Confirm your choices and let us handle the booking.",
      isFirst: false,
      isLast: true,
    },
  ];

  return (
    <Card className="border-none shadow-none">
      <CardContent className="p-0">
        <div className="flex flex-col items-start w-full">
          <div className="flex flex-col w-full max-w-[960px] mx-auto">
            <div className="flex flex-col h-[60px] items-start pt-5 pb-3 px-4 w-full">
              <h2 className="font-bold text-[22px] leading-7 font-['Plus_Jakarta_Sans',Helvetica]">
                How it works
              </h2>
            </div>

            <div className="flex flex-col gap-4 px-4 py-0 w-full">
              {timelineSteps.map((step, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 50 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{
                    duration: 0.6,
                    ease: "easeOut",
                    delay: index * 0.2,
                  }}
                  viewport={{ once: false, amount: 0.3 }}
                  className="flex items-start gap-4 w-full"
                >
                  {/* Step marker */}
                  <div className="flex flex-col w-10 items-center gap-1 self-stretch">
                    {!step.isFirst && (
                      <div className="w-0.5 flex-1 bg-green" />
                    )}
                    <div className="w-6 h-6 rounded-full bg-green text-white flex items-center justify-center text-xs font-bold">
                      {index + 1}
                    </div>
                    {!step.isLast && (
                      <div className="w-0.5 flex-1 bg-green" />
                    )}
                  </div>

                  {/* Step content */}
                  <div className="flex flex-col items-start py-3 flex-1">
                    <h3 className="font-medium text-base leading-6 font-['Plus_Jakarta_Sans',Helvetica]">
                      {step.title}
                    </h3>
                    <p className="font-normal text-base leading-6 font-['Plus_Jakarta_Sans',Helvetica]">
                      {step.description}
                    </p>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
