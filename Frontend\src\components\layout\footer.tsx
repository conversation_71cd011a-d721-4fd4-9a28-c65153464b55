
import { FacebookIcon, InstagramIcon, TwitterIcon } from "lucide-react";

export const Footer = () => {
  // Footer navigation links
  const footerLinks = [
    { title: "About" },
    { title: "Contact" },
    { title: "Privacy" },
    { title: "Terms" },
  ];

  // Social media icons
  const socialIcons = [
    { icon: <FacebookIcon size={24} /> },
    { icon: <TwitterIcon size={24} /> },
    { icon: <InstagramIcon size={24} /> },
  ];

  return (
    <footer className="flex flex-col items-start gap-6 px-5 py-10 w-full  ">
      {/* Footer navigation */}
      <nav className="flex flex-wrap items-center justify-between gap-[24px_24px] w-full">
        {footerLinks.map((link, index) => (
          <div key={index} className="flex-col w-40 items-center flex">
            <a
              href="#"
              className="self-stretch mt-[-1.00px] font-normal text-base text-center leading-6 hover:underline"
            >
              {link.title}
            </a>
          </div>
        ))}
      </nav>

      {/* Social media icons */}
      <div className="flex flex-wrap items-start gap-4 w-full justify-center">
        {socialIcons.map((item, index) => (
          <a
            key={index}
            href="#"
            className="inline-flex items-center justify-center text-[#607589] hover:text-primary transition-colors"
          >
            {item.icon}
          </a>
        ))}
      </div>

      <div className="flex items-center w-full justify-center">
        <p className="font-normal text-base text-center leading-6">
          © {new Date().getFullYear()} Travel AI. All rights reserved.
        </p>
      </div>
    </footer>
  );
};

