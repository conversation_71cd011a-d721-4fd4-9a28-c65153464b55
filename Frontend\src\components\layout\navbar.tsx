import { useState } from 'react';
import { Link } from 'react-router-dom';
import ThemeToggle from '../mini-components/theme-toggle';

const Navbar = () => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <header className="w-full bg-light text-dark dark:bg-dark dark:text-light border-b border-dark dark:border-light">
      <div className="max-w-7xl mx-auto px-4 py-4 flex items-center justify-between">
        <Link to="/" className="text-2xl font-bold text-secondary">Travel AI</Link>

        {/* Hamburger toggle: visible on mobile only */}
        <button
          className="md:hidden p-2 focus:outline-none"
          aria-label="Toggle menu"
          onClick={() => setIsOpen(!isOpen)}
        >
          <svg
            className="w-6 h-6"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            {isOpen
              ? <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
              : <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 8h16M4 16h16" />}
          </svg>
        </button>

        {/* Desktop menu */}
        <nav className="hidden md:flex gap-6 items-center">
          <Link to="#features" className="hover:text-green">Features</Link>
          <Link to="#about" className="hover:text-green">About</Link>
          <Link to="#contact" className="hover:text-green">Contact</Link>

          {/* Auth links */}
          <Link to="/signup" className="hover:text-green hover:bg-light px-4 py-2 rounded-full bg-green text-light font-medium">Sign up</Link>
          <Link to="/signin" className="hover:text-green px-4 py-2 rounded-full bg-secondary text-light font-medium">Sign in</Link>

          <ThemeToggle />
        </nav>
      </div>

      {/* Mobile menu */}
      <nav className={`md:hidden px-4 pb-4 space-y-2 transition-max-h duration-300 ease-in-out overflow-hidden ${isOpen ? 'max-h-52' : 'max-h-0'}`}>
        <Link to="#features" className="block py-2 hover:text-green" onClick={() => setIsOpen(false)}>Features</Link>
        <Link to="#about" className="block py-2 hover:text-green" onClick={() => setIsOpen(false)}>About</Link>
        <Link to="#contact" className="block py-2 hover:text-green" onClick={() => setIsOpen(false)}>Contact</Link>

        {/* Auth links mobile */}
        <Link to="/signup" className="block px-4 py-2 hover:text-green  font-medium" onClick={() => setIsOpen(false)}>Sign up</Link>
        <Link to="/signin" className="block py-2 hover:text-green px-4   font-medium" onClick={() => setIsOpen(false)}>Sign in</Link>

        <ThemeToggle />
      </nav>
    </header>
  );
};

export default Navbar;
