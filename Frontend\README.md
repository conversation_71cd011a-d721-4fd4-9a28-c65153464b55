# ByteBound Frontend

Welcome to the **ByteBound** frontend! 
This is our AI-powered travel planning web app for the **Raise Your Hack** hackathon.  
It helps travelers plan trips with an AI chatbot, book hotels & flights, and save their preferences — all with a smooth, animated UI.

---

## ✨ Features

✅ AI chatbot travel agent  
✅ Beautiful, responsive design with Tailwind CSS  
✅ Smooth animations with Framer Motion  
✅ Save user preferences for future trips  
✅ Built with React.js + Vite for super fast dev experience

---

## 📂 Project Structure

bytebound/
Frontend/
├── public/ # Static files (images, icons, etc.)
├── src/
│ ├── components/ # Reusable components (Testimonials, Pricing, CTA, etc.)
│ ├── ui/ # Shared UI primitives (Button, Card, etc.)
│ ├── App.tsx # Main app entry
│ └── main.tsx # Vite entry point
├── package.json
├── vite.config.js
├── tailwind.config.js
└── ...


---

## 🚀 Getting Started

### 1️⃣ Clone the repo

```bash
git clone https://github.com/franceskora/bytebound.git
cd bytebound/Frontend

2️⃣ Install dependencies

npm install
# or
yarn install

3️⃣ Run the development server

npm run dev
# or
yarn dev

Open http://localhost:5173 to see the app!
⚙️ Tech Stack

    Framework: React.js (with Vite)

    Styling: Tailwind CSS

    Animations: Framer Motion

    Icons: Lucide React

 Deployment: Vercel / Netlify / any static host

Build for Production

npm run build
npm run preview

Contributing

This is a collaborative project for the Raise Your Hack hackathon.

Clone the repo and create a branch:

git checkout -b your-branch-name

Make your changes and commit:

git commit -m "Add feature XYZ"

Push and open a Pull Request:

git push origin your-branch-name

🙌 Team ByteBound

Built by the ByteBound team for the Raise Your Hack hackathon.


For questions, reach out to any team member on GitHub!

