import { motion } from "framer-motion";
import { Card, CardContent } from "../../ui/card";

export const Testimonials = () => {
  const testimonials = [
    {
      image: "/testimonials/sophia.png",
      quote:
        '"I\'ve never had a trip planned so easily. I just told the AI where I wanted to go, and it took care of everything."',
      name: "<PERSON>, 32",
    },
    {
      image: "/testimonials/ethan.png",
      quote:
        '"I was skeptical at first, but the AI found the perfect hotel and flights for me. I\'ll definitely use it again."',
      name: "<PERSON>, 28",
    },
    {
      image: "/testimonials/olivia.png",
      quote:
        '"I love that I can save my preferences and the AI will remember them for future trips."',
      name: "<PERSON>, 25",
    },
  ];

  return (
    <section className="w-full py-10">
      <div className="flex flex-col md:flex-row gap-4 p-4 w-full">
        {testimonials.map((testimonial, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{
              duration: 0.6,
              ease: "easeOut",
              delay: index * 0.2,
            }}
            viewport={{ once: false, amount: 0.3 }}
            className="flex flex-1"
          >
            <Card className="flex flex-col flex-1 w-full md:w-auto rounded-lg border-none">
              <CardContent className="p-0">
                <div
                  className="w-full h-[301px] rounded-xl mb-4"
                  style={{
                    background: `url(${testimonial.image}) center/cover`,
                  }}
                />
                <div className="flex flex-col px-4 pb-4">
                  <p className="font-medium text-base leading-6 font-['Plus_Jakarta_Sans',Helvetica]">
                    {testimonial.quote}
                  </p>
                  <p className="font-normal text-sm leading-[21px] font-['Plus_Jakarta_Sans',Helvetica] mt-2">
                    {testimonial.name}
                  </p>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>
    </section>
  );
};
